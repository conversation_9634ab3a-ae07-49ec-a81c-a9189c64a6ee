import React, { useState } from 'react';
import { Modal, Radio, Button, Icon, message, Divider } from 'antd';
import request from '@/utils/axios';
import './ExportDialog.scss';

const ExportDialog = ({ visible, onClose, fileData, fileId }) => {
  const [exportFormat, setExportFormat] = useState('excel');
  const [exporting, setExporting] = useState(false);

  // 导出格式选项
  const exportOptions = [
    {
      value: 'excel',
      label: 'Excel 表格',
      icon: 'file-excel',
      description: '导出为 Excel 文件，包含完整的表格数据',
      extension: '.xlsx .xls',
      color: '#67c23a',
      type: 1
    },
    {
      value: 'xmind',
      label: 'XMind 思维导图',
      icon: 'bar-chart',
      description: '导出为 XMind 文件，保持思维导图结构',
      extension: '.xmind',
      color: '#409eff',
      type: 2
    },
  ];

  const handleExport = async () => {
    if (!fileData || !fileId) {
      message.error('没有可导出的数据');
      return;
    }

    try {
      setExporting(true);

      // 获取选中的导出格式
      const selectedOption = exportOptions.find(opt => opt.value === exportFormat);

      // 判断是转换记录还是用例
      const idType = fileData.fileType === 'case' ? 2 : 1;

      // 直接使用axios进行文件下载，设置responseType为blob
      const response = await request('/docTransfer/exportFile', {
        method: 'GET',
        params: {
          id: fileId,
          idType: idType,
          type: selectedOption.type
        },
        responseType: 'blob' // 设置响应类型为blob以处理文件流
      });

      // 检查响应是否成功
      if (response && response.status === 200) {
        // 使用fileData.name作为文件名，格式使用响应体中返回的格式
        let fileName;
        const baseName = fileData.name.replace(/\.[^/.]+$/, '');

        if (response.fileExtension) {
          // 如果响应体中有文件扩展名，则使用它
          fileName = `${baseName}_export.${response.fileExtension}`;
        } else {
          // 如果没有响应扩展名，使用选择的导出格式
          fileName = `${baseName}${selectedOption.extension.split(' ')[0]}`;
        }

        // 创建下载链接
        const blob = new Blob([response.data]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        message.success(`文件导出成功,请在浏览器下载界面查看下载内容~`);
        onClose();
      } else {
        message.error('导出失败');
      }
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败：' + (error.message || '未知错误'));
    } finally {
      setExporting(false);
    }
  };

  const handleFormatChange = (e) => {
    setExportFormat(e.target.value);
  };

  return (
    <Modal
      title="导出文件"
      visible={visible}
      onCancel={onClose}
      width={600}
      className="export-dialog"
      footer={[
        <Button key="cancel" onClick={onClose} disabled={exporting}>
          取消
        </Button>,
        <Button
          key="export"
          type="primary"
          onClick={handleExport}
          loading={exporting}
          disabled={!fileData || !fileId}
        >
          {/* <Icon type={exporting ? 'loading' : 'download'} /> */}
          {exporting ? '导出中...' : '确认导出'}
        </Button>
      ]}
    >
      <div className="export-content">
        {/* 导出格式选择 */}
        <div className="export-format-section">
          <h4>选择导出格式</h4>
          <Radio.Group
            value={exportFormat}
            onChange={handleFormatChange}
            className="format-radio-group"
          >
            {exportOptions.map(option => (
              <Radio.Button
                key={option.value}
                value={option.value}
                className="format-option"
              >
                <div className="format-content">
                  <div className="format-header">
                    <Icon
                      type={option.icon}
                      style={{ color: option.color }}
                      className="format-icon"
                    />
                    <span className="format-label">{option.label}</span>
                    <span className="format-extension">{option.extension}</span>
                  </div>
                  <div className="format-description">
                    {option.description}
                  </div>
                </div>
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
      </div>
    </Modal>
  );
};


export default ExportDialog; 
