.converter-home {
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;
  padding: 12px;

  .main-container {
    display: flex;
    height: 100%;
    gap: 12px;
    transition: all 0.3s ease;
    overflow: visible;

    /* 左侧面板收缩时的布局调整 */
    &.left-panel-collapsed {
      gap: 0;
      
      .right-panel {
        margin-left: 30px; /* 为切换按钮留出空间 */
      }
    }
  }
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;

  .content-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    /* 上传区域样式 */
    .upload-section {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;
    }
  }
}

/* 预览区域样式 */
.preview-section {
  flex: 1;
  display: flex;
  flex-direction: column;

  .preview-toolbar {
    padding: 7px 26px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        border: 1px solid #dcdfe6;
        background: white;
        color: #606266;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;

        &:hover {
          color: #409eff;
          border-color: #409eff;
        }

        &:focus {
          color: #409eff;
          border-color: #409eff;
        }
      }

      .current-file-info {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .file-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
        }

        .file-type {
          font-size: 12px;
          color: #67c23a;
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }
}

.mode-tabs {
  display: flex;
  gap: 8px;

  .mode-radio-group {
    display: flex;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .ant-radio-button-wrapper {
      position: relative;
      // padding: 10px 16px;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 6px;
      transition: all 0.2s ease;
      border: 1px solid #dcdfe6;
      background: white;
      color: #606266;
      border-radius: 0;
      min-width: auto;

      &:hover {
        color: #409eff;
        border-color: #b3d8ff;
        background: #ecf5ff;
      }

      &:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }

      &:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }

      &:not(:first-child) {
        border-left: none;
      }

      &.ant-radio-button-wrapper-checked {
        background: #409eff !important;
        border-color: #409eff !important;
        color: white !important;
        z-index: 1;
      }

      .mode-radio-button {
        position: relative;

        span {
          white-space: nowrap;
          
        }
      }
    }
  }
}

.export-button {
  padding: 8px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  background: #67c23a !important;
  border-color: #67c23a !important;
  color: white !important;
  
  &:hover {
    background: #85ce61 !important;
    border-color: #85ce61 !important;
  }
  
  &:focus {
    background: #85ce61 !important;
    border-color: #85ce61 !important;
  }
  
  &:active {
    background: #5daf34 !important;
    border-color: #5daf34 !important;
  }
}

.preview-content {
  flex: 1;
  position: relative;
  overflow: hidden;

  /* XMind 预览样式 */
  .xmind-preview {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1;

    &.active {
      opacity: 1;
      visibility: visible;
      z-index: 2;
    }

    &.hidden {
      opacity: 0;
      visibility: hidden;
      z-index: 1;
      pointer-events: none; /* 防止隐藏时仍能交互 */
    }
  }

  /* 表格预览样式 */
  .table-preview {
    padding: 10px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    background: #f8f9fa;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1;

    &.active {
      opacity: 1;
      visibility: visible;
      z-index: 2;
    }

    &.hidden {
      opacity: 0;
      visibility: hidden;
      z-index: 1;
      pointer-events: none; /* 防止隐藏时仍能交互 */
    }
  }
}

/* 自定义表格样式 */
.custom-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  background: white;

  .ant-table-thead > tr > th {
    border-right: 1px solid #e4e7ed;
    border-bottom: 2px solid #e4e7ed;
    background: #f8f9fa !important;
    font-weight: 600;
    font-size: 14px;
    text-align: center;
    color: #303133;
  }

  .ant-table-tbody > tr > td {
    border-right: 1px solid #f0f0f0;
    vertical-align: top;
    line-height: 1.6;
  }

  .ant-table-tbody > tr {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f7fa !important;
    }

    &:nth-child(even) {
      background-color: #fafbfc;

      &:hover {
        background-color: #f0f4f8 !important;
      }
    }
  }

  .ant-table-bordered {
    border: 1px solid #e4e7ed;
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0;
  }

  /* 表格内容样式优化 */
  .ant-table-cell {
    white-space: pre-line;
    word-break: break-word;
    padding: 12px 8px;
  }

  /* 空单元格样式 */
  .ant-table-placeholder {
    color: #c0c4cc;
    font-style: italic;
  }
}

/* 表格内容换行样式 */
.table-cell-content {
  white-space: pre-line;
  word-break: break-word;
  padding: 4px 0;
}

/* 全局样式覆盖 */
.ant-layout-content {
  padding: 0 !important;
}

/* Ant Design 组件样式覆盖 */
.ant-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.ant-radio-group {
  .ant-radio-button-wrapper {
    height: auto;
  }
}

/* 表格滚动样式 */
.ant-table-body {
  // scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.ant-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  color: #909399;
  
  .ant-spin {
    margin-bottom: 16px;
  }
  
  div {
    font-size: 16px;
  }
} 