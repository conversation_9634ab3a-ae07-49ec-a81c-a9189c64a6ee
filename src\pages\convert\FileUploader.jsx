import React, { useState, useEffect } from 'react';
import { Upload, Button, Icon, message, Radio } from 'antd';
import request from '@/utils/axios';
import './FileUploader.scss';

const { Dragger } = Upload;

const FileUploader = ({ onFileUploaded, productLineId, onRefreshRecords }) => {
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [parseMode, setParseMode] = useState(1); // 1: 严格模式, 2: 兼容模式

  // 支持的文件类型
  const supportedTypes = ['.xmind', '.xlsx', '.xls', '.csv'];

  // 文件上传前的检查
  const beforeUpload = (file) => {
    const isValidType = supportedTypes.some(type => 
      file.name.toLowerCase().endsWith(type)
    );
    
    if (!isValidType) {
      message.error('上传失败,不支持当前文件格式,请上传支持的文件格式：.xmind, .xlsx, .xls, .csv');
      return false;
    }

    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
      message.error('文件大小不能超过 50MB');
      return false;
    }

    return true;
  };

  // 处理文件上传
  const handleUpload = async (info) => {
    const { file, fileList: newFileList } = info;
    
    if (file.status === 'uploading') {
      setUploading(true);
      setFileList(newFileList);
      return;
    }

    if (file.status === 'done') {
      setUploading(false);
      setFileList([]);
      
      // 通知父组件文件上传完成
      if (onFileUploaded) {
        onFileUploaded(file.originFileObj);
      }

      // 通知父组件刷新转换记录
      if (onRefreshRecords) {
        onRefreshRecords();
      }

      message.success('文件上传成功');
    }

    if (file.status === 'error') {
      setUploading(false);
      setFileList([]);
      message.error(`${file.name} 上传失败`);
    }
  };

  // 真实上传逻辑
  const customRequest = async ({ file, onSuccess, onError }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('channel', '1');
      formData.append('productLineId', productLineId);
      formData.append('companyId', productLineId);
      formData.append('parseMode', parseMode);

      const response = await request('/docTransfer/convertUploadFile', {
        method: 'POST',
        body: formData,
      });

      if (response.code === 200) {
        onSuccess(response, file);
      } else {
        onError(new Error(response.msg || '上传失败'));
      }
    } catch (error) {
      onError(error);
    }
  };

  return (
    <div className="file-uploader">
      <div className="upload-area">
        <Dragger
          accept={supportedTypes.join(',')}
          beforeUpload={beforeUpload}
          customRequest={customRequest}
          onChange={handleUpload}
          fileList={fileList}
          className="upload-dragger"
          disabled={uploading}
          showUploadList={false}
        >
          <div className="upload-content">
            {uploading ? (
              <>
                <Icon type="loading" className="upload-icon loading" />
                <p className="upload-title">文件上传中...</p>
                <p className="upload-description">请稍等，正在处理您的文件</p>
              </>
            ) : (
              <>
                <Icon type="cloud-upload" className="upload-icon" />
                <p className="upload-title">拖拽文件到此处开始转换</p>
                
                <div className="parse-mode-section">
                  <div className="mode-label">文件解析模式</div>
                  <Radio.Group 
                    value={parseMode} 
                    onChange={(e) => setParseMode(e.target.value)}
                    className="mode-radio-group"
                  >
                    <Radio.Button value={1} className="mode-radio-button">
                      严格模式
                    </Radio.Button>
                    <Radio.Button value={2} className="mode-radio-button">
                      兼容模式
                    </Radio.Button>
                  </Radio.Group>
                </div>

                <div className="upload-note">
                  <Icon type="info-circle" className="el-icon" />
                  <span>支持 .xmind, .xlsx, .xls, .csv格式文件</span>
                </div>
                
                <Button
                  type="primary"
                  size="large"
                  disabled={uploading}
                  className="select-button"
                >
                  选择文件
                </Button>
              </>
            )}
          </div>
        </Dragger>
      </div>
    </div>
  );
};

export default FileUploader; 
