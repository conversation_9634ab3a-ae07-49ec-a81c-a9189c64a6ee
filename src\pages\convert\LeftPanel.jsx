import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Button, Icon, Badge, Spin, message, Tree } from 'antd';
import request from '@/utils/axios';
import './LeftPanel.scss';

const { TreeNode } = Tree;

const LeftPanel = forwardRef(({
  selectedIndex,
  onSelectFile,
  onDeleteFile,
  isCollapsed,
  onTogglePanel,
  productLineId,
  onModeChange // 新增：模式切换回调
}, ref) => {
  const [mode, setMode] = useState('records'); // 'records' | 'cases'
  const [files, setFiles] = useState([]);
  const [caseTree, setCaseTree] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize] = useState(50);
  const [selectedCaseId, setSelectedCaseId] = useState(null);

  // 暴露刷新方法给父组件
  useImperativeHandle(ref, () => ({
    refreshData: mode === 'records' ? refreshAndSelectFirst : fetchCaseTree,
    switchToRecordsAndRefresh: switchToRecordsAndRefresh
  }));
  // 获取转换记录列表
  const fetchTransferList = async () => {
    if (!productLineId) return;
    
    setLoading(true);
    try {
      const response = await request('/docTransfer/queryList', {
        method: 'GET',
        params: {
          pageNum: pageNum,
          pageSize: pageSize,
          channel: 1,
          productLineId: productLineId,
          companyId: productLineId
        }
      });

      if (response.code === 200) {
        const transformedFiles = response.data.map(item => ({
          id: item.id,
          name: item.originalName || '未知文件',
          type: item.type || 'unknown',
          uploadTime: item.gmtCreated,
          conversion: item.typeName,
          fileType:'record'
        })) || [];       
        setFiles(transformedFiles);
      } else {
        message.error(response.msg || '获取转换记录失败');
      }
    } catch (error) {
      console.error('获取转换记录失败:', error);
      message.error('获取转换记录失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 获取项目用例树形数据
  const fetchCaseTree = async () => {
    if (!productLineId) return;
    
    setLoading(true);
    try {
      const response = await request('/case/reviewCaseList', {
        method: 'GET',
        params: {
          productLineId: productLineId,
          channel: 1,
          companyId: productLineId,
        }
      });

      if (response.code === 200) {
        const treeData = convertToTreeData(response.data);
        setCaseTree(treeData);
      } else {
        message.error(response.msg || '获取项目用例失败');
      }
    } catch (error) {
      console.error('获取项目用例失败:', error);
      message.error('获取项目用例失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 将接口数据转换为树形结构
  const convertToTreeData = (data) => {
    const convertNode = (node) => {
      const result = {
        id: node.id,
        name: node.text,
        type: 'folder',
        children: []
      };

      // 如果有用例列表，添加用例作为叶子节点
      if (node.caseDtoList && node.caseDtoList.length > 0) {
        node.caseDtoList.forEach(caseItem => {
          result.children.push({
            id: caseItem.id.toString(),
            name: caseItem.title,
            type: 'case',
            fileType: 'case', // 标识为用例类型
            isLeaf: true
          });
        });
      }

      // 如果有子文件夹，递归转换
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          result.children.push(convertNode(child));
        });
      }

      // 如果没有children或children为空数组，删除children属性
      if (!result.children || result.children.length === 0) {
        delete result.children;
        // 如果是空文件夹，设置为叶子节点但不可选择
        if (!node.caseDtoList || node.caseDtoList.length === 0) {
          result.isLeaf = true;
          result.type = 'empty-folder';
        }
      }

      return result;
    };

    return [convertNode(data)];
  };

  // 切换模式
  const toggleMode = () => {
    const newMode = mode === 'records' ? 'cases' : 'records';
    setMode(newMode);
    setSelectedCaseId(null);

    // 通知父组件模式已切换，需要重置右侧区域
    if (onModeChange) {
      onModeChange(newMode);
    }

    // 移除这里的接口调用，让 useEffect 统一处理
    // if (newMode === 'cases') {
    //   fetchCaseTree();
    // } else {
    //   fetchTransferList();
    // }
  };

  // 组件加载时获取数据
  useEffect(() => {
    if (mode === 'records') {
      fetchTransferList();
    } else {
      fetchCaseTree();
    }
  }, [productLineId, pageNum, mode]); // mode 变化时会自动触发

  // 获取文件图标
  const getFileIcon = (type) => {
    switch (type) {
      case 'folder':
      case 'empty-folder':
        return 'folder';
      case 'case':
        return 'file-text';
        case 1:
          return 'file-text';
      case 2:
        return 'file-excel';
      default:
        return 'file';
    }
  };

  // 获取文件类型标签颜色
  const getFileTypeColor = (type) => {
    switch (type) {
      case 'case':
        return '#1890ff';
      case 'folder':
        return '#faad14';
      case 'empty-folder':
        return '#d9d9d9';
      case 1:
        return '#1890ff';
      case 2:
        return '#52c41a';
      default:
        return '#909399';
    }
  };

  // 处理文件选择
  const handleSelectFile = (index) => {
    const file = files[index];
    onSelectFile(file.id, index, file, 'records');
  };

  // 处理用例选择
  const handleSelectCase = (selectedKeys, info) => {
    if (selectedKeys.length > 0) {
      const caseId = selectedKeys[0];
      const caseInfo = info.node.props.dataRef;
      
      // 只有用例类型才能选择，文件夹不能选择
      if (caseInfo && caseInfo.type === 'case') {
        setSelectedCaseId(caseId);
        onSelectFile(caseId, -1, caseInfo, 'cases');
      }
    }
  };

  // 处理文件删除
  const handleDeleteFile = async (index, file) => {
    try {
      // 调用删除接口
      const response = await request('/docTransfer/delete', {
        method: 'POST',
        body: {
          id: file.id
        }
      });

      if (response.code === 200) {
        message.success('删除成功');

        // 通知父组件处理删除逻辑
        onDeleteFile(index, file);

        // 刷新记录列表并在完成后选择第一个记录
        await refreshAndSelectFirst();

      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      message.error('删除失败，请检查网络连接');
    }
  };

  // 刷新记录列表并选择第一个记录
  const refreshAndSelectFirst = async () => {
    if (!productLineId) return;
    setLoading(true);
    try {
      const response = await request('/docTransfer/queryList', {
        method: 'GET',
        params: {
          pageNum: pageNum,
          pageSize: pageSize,
          channel: 1,
          productLineId: productLineId,
          companyId: productLineId
        }
      });

      if (response.code === 200) {
        const transformedFiles = response.data.map(item => ({
          id: item.id,
          name: item.originalName || '未知文件',
          type: item.type || 'unknown',
          uploadTime: item.gmtCreated,
          conversion: item.typeName,
          fileType:'record'
        })) || [];

        setFiles(transformedFiles);

        // 如果有记录，自动选择第一个
        if (transformedFiles.length > 0) {
          const firstFile = transformedFiles[0];
          onSelectFile(firstFile.id, 0, firstFile, 'records');
        }
      } else {
        message.error(response.msg || '获取转换记录失败');
      }
    } catch (error) {
      console.error('获取转换记录失败:', error);
      message.error('获取转换记录失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 专门用于文件上传后的处理：切换到records模式并刷新数据
  const switchToRecordsAndRefresh = async () => {
    // 如果当前不是records模式，先切换模式并通知父组件
    if (mode !== 'records') {
      setMode('records');
      setSelectedCaseId(null);

      // 通知父组件模式已切换，需要重置右侧区域
      if (onModeChange) {
        onModeChange('records');
      }
    }

    // 刷新记录列表并选择第一个
    await refreshAndSelectFirst();
  };

  // 渲染树节点
  const renderTreeNodes = (data) => {
    return data.map(item => {
      const isSelectable = item.type === 'case';
      
      if (item.children) {
        return (
          <TreeNode
            title={
              <span>
                <Icon 
                  type={getFileIcon(item.type)} 
                  style={{ color: getFileTypeColor(item.type), marginRight: 8 }}
                />
                {item.name}
              </span>
            }
            key={item.id}
            dataRef={item}
            selectable={isSelectable}
          >
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          title={
            <span>
              <Icon 
                type={getFileIcon(item.type)} 
                style={{ color: getFileTypeColor(item.type), marginRight: 8 }}
              />
              {item.name}
            </span>
          }
          key={item.id}
          dataRef={item}
          selectable={isSelectable}
          isLeaf
        />
      );
    });
  };

  return (
    <div className={`left-panel ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="panel-header">
        <div className="header-title">
          <Icon type={mode === 'records' ? 'folder' : 'file-text'} />
          <span>{mode === 'records' ? '转换记录' : '项目用例选择'}</span>
        </div>
        <Button 
          type="link" 
          size="small" 
          onClick={toggleMode}
          className="mode-toggle-btn"
        >
          {/* <Icon type={mode === 'records' ? 'file-text' : 'folder'} /> */}
          {mode === 'records' ? '项目用例' : '转换记录'}
        </Button>
      </div>
      
      <div className="panel-content">
        {loading ? (
          <div className="loading-container">
            <Spin />
            <div>加载中...</div>
          </div>
        ) : mode === 'records' ? (
          <div className="file-list">
            {files.length === 0 ? (
              <div className="empty-state">
                <Icon type="inbox" className="empty-icon" />
                <div className="empty-title">暂无转换记录</div>
                <div className="empty-description">上传文件后，转换记录将显示在这里</div>
              </div>
            ) : (
              files.map((file, index) => (
                <div
                  key={file.id}
                  className={`file-item ${selectedIndex === index ? 'active' : ''}`}
                  onClick={() => handleSelectFile(index)}
                >
                  <div className="file-info">
                    <div className="file-icon">
                      <Icon 
                        type={getFileIcon(file.type)} 
                        style={{ color: getFileTypeColor(file.type) }}
                      />
                    </div>
                    
                    <div className="file-details">
                      <div className="file-name" title={file.name}>
                        {file.name}
                      </div>
                      <div className="file-meta">
                        {file.uploadTime}
                      </div>
                      <div className="file-conversion">
                        {file.conversion}
                      </div>
                    </div>
                  </div>
                  
                  <div className="file-actions">
                    <Icon
                      type="delete"
                      className="delete-icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteFile(index, file);
                      }}
                    />
                  </div>
                </div>
              ))
            )}
          </div>
        ) : (
          <div className="case-tree">
            {caseTree.length === 0 ? (
              <div className="empty-state">
                <Icon type="file-text" className="empty-icon" />
                <div className="empty-title">暂无项目用例</div>
                <div className="empty-description">项目中还没有用例文件</div>
              </div>
            ) : (
              <Tree
                showLine
                defaultExpandedKeys={['root']}
                selectedKeys={selectedCaseId ? [selectedCaseId] : []}
                onSelect={handleSelectCase}
              >
                {renderTreeNodes(caseTree)}
              </Tree>
            )}
          </div>
        )}
      </div>
      
      {/* 切换按钮 */}
      <div className="panel-toggle" onClick={onTogglePanel}>
        <Icon 
          type="left" 
          className={`toggle-icon ${isCollapsed ? 'rotated' : ''}`} 
        />
      </div>
    </div>
  );
});

export default LeftPanel; 




