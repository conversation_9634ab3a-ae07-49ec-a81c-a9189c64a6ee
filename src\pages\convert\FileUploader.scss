.file-uploader {
  width: 100%;
  margin: 0 auto;
  
  .upload-area {
    border: 3px dashed #dcdfe6;
    border-radius: 16px;
    padding: 120px 80px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
    max-width: 1200px;
    width: 100%;
    min-height: 400px;
    margin: 0 auto;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
      
      .upload-content .upload-icon {
        color: #409eff;
        transform: scale(1.05);
      }
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 24px;

      .upload-icon {
        font-size: 64px;
        color: #409eff;
        margin-bottom: 8px;
        display: block;
        transition: all 0.3s ease;

        &.loading {
          color: #409eff;
        }
      }

      .upload-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      .upload-description {
        margin: 0;
        font-size: 16px;
        color: #909399;
        margin-bottom: 8px;
      }

      .parse-mode-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        margin: 16px 0;

        .mode-label {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin: 0;
        }

        .mode-radio-group {
          .ant-radio-button-wrapper {
            border-radius: 20px !important;
            border: 2px solid #e4e7ed !important;
            background: #ffffff !important;
            color: #606266 !important;
            font-weight: 500;
            padding: 8px 20px;
            height: auto !important;
            line-height: 1.4;
            margin: 0 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

            &:first-child {
              border-radius: 20px !important;
              margin-left: 0;
            }

            &:last-child {
              border-radius: 20px !important;
              margin-right: 0;
            }

            &:hover {
              border-color: #409eff !important;
              color: #409eff !important;
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
            }

            &.ant-radio-button-wrapper-checked {
              border-color: #409eff !important;
              background: #409eff !important;
              color: #ffffff !important;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

              &:hover {
                border-color: #409eff !important;
                background: #409eff !important;
                color: #ffffff !important;
                transform: translateY(-1px);
              }

              &::before {
                display: none !important;
              }
            }

            .ant-radio-button {
              display: none !important;
            }
          }
        }
      }

      .upload-note {
        margin: 0;
        font-size: 14px;
        color: #606266;
        background: #f0f9ff;
        padding: 8px 16px;
        border-radius: 6px;
        border-left: 3px solid #409eff;
        display: flex;
        align-items: center;
        gap: 6px;
        max-width: 500px;
        margin-bottom: 16px;
        
        .el-icon {
          color: #409eff;
          font-size: 16px;
        }
      }

      .select-button {
        padding: 12px 32px;
        font-size: 16px;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
        }
      }
    }
  }

  .upload-progress {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .progress-text {
      margin: 12px 0 0 0;
      text-align: center;
      color: #606266;
      font-size: 14px;
    }
  }


}

/* 上传中状态样式 */
.upload-area.uploading {
  border-color: #409eff;
  background: #f0f9ff;
  pointer-events: none;
}

/* 拖拽状态样式 */
.upload-area.drag-over {
  border-color: #52c41a;
  background: #f6ffed;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .file-uploader {
    .upload-area {
      padding: 60px 40px;
      min-height: 300px;

      .upload-content {
        gap: 16px;

        .upload-icon {
          font-size: 48px;
        }

        .upload-title {
          font-size: 20px;
        }

        .upload-description {
          font-size: 14px;
        }

        .parse-mode-section {
          margin: 12px 0;

          .mode-label {
            font-size: 14px;
          }

          .mode-radio-group {
            .ant-radio-button-wrapper {
              padding: 6px 16px;
              font-size: 12px;
              margin: 0 2px;
            }
          }
        }

        .upload-note {
          font-size: 12px;
          padding: 6px 12px;
        }

        .select-button {
          padding: 10px 24px;
          font-size: 14px;
        }
      }
    }
  }
}

/* Ant Design Upload 组件样式覆盖 */
.upload-area {
  .ant-upload-drag {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    height: 100% !important;
    width: 100% !important;

    .ant-upload {
      padding: 0 !important;
      background: transparent !important;
      height: 100% !important;
      width: 100% !important;
    }
  }

  .ant-upload-drag-icon {
    margin-bottom: 0 !important;
  }

  .ant-upload-text {
    margin-bottom: 0 !important;
  }

  .ant-upload-hint {
    margin-top: 0 !important;
  }
} 